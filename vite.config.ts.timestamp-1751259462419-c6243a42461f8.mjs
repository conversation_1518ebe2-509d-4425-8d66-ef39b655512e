// vite.config.ts
import path from "path";
import process from "process";
import react from "file:///home/<USER>/Desktop/appetec-admin/node_modules/.pnpm/@vitejs+plugin-react@4.3.1_vite@5.4.19_@types+node@22.5.4_sass@1.89.0_sugarss@2.0.0_terser@5.39.2_/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { defineConfig } from "file:///home/<USER>/Desktop/appetec-admin/node_modules/.pnpm/vite@5.4.19_@types+node@22.5.4_sass@1.89.0_sugarss@2.0.0_terser@5.39.2/node_modules/vite/dist/node/index.js";
import { createSvgIconsPlugin } from "file:///home/<USER>/Desktop/appetec-admin/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.4.19_@types+node@22.5.4_sass@1.89.0_sugarss@2.0.0_terser@5.39.2_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import tsconfigPaths from "file:///home/<USER>/Desktop/appetec-admin/node_modules/.pnpm/vite-tsconfig-paths@5.1.4_typescript@5.8.3_vite@5.4.19_@types+node@22.5.4_sass@1.89.0_sugarss@2.0.0_terser@5.39.2_/node_modules/vite-tsconfig-paths/dist/index.js";
var vite_config_default = defineConfig({
  base: "./",
  esbuild: {
    // drop: ['console', 'debugger'],
  },
  css: {
    // Enable CSS sourcemap for easier CSS debugging
    devSourcemap: true
  },
  plugins: [
    react(),
    // Synchronize the path settings alias in tsconfig.json
    tsconfigPaths(),
    createSvgIconsPlugin({
      // Specify the folder for icons that need to be cached
      iconDirs: [path.resolve(process.cwd(), "src/assets/icons")],
      // Specify symbolId format
      symbolId: "icon-[dir]-[name]"
    })
  ],
  server: {
    // Automatically open the browser
    open: true,
    host: true,
    port: 5174
    // proxy: {
    //   '/api': {
    //     target: 'http://localhost:8001', //for use the original backend server change the target url
    //     changeOrigin: true,
    //     // rewrite: (path) => path.replace(/^\/api/, ''),
    //   },
    // },
  },
  build: {
    target: "esnext",
    minify: "terser",
    terserOptions: {
      compress: {
        // Remove console in the production environment
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
