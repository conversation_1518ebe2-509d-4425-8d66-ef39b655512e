# Appetec Server - Development Setup Guide

## Prerequisites

### Required Software
- **Node.js**: v18.x or higher
- **npm**: v8.x or higher (comes with Node.js)
- **MongoDB**: v5.x or higher
- **Redis**: v6.x or higher
- **Git**: Latest version

### Optional Tools
- **MongoDB Compass**: GUI for MongoDB
- **Redis CLI**: Command line interface for Redis
- **Postman**: API testing tool
- **Docker**: For containerized development

## Environment Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd appetec-server
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create a `.env` file in the root directory:

```env
# Server Configuration
SERVER_PORT=4000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/appetec
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=your-refresh-token-secret
JWT_REFRESH_EXPIRES_IN=7d

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key
ENCRYPTION_IV=your-16-character-iv

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name

# Email Configuration (AWS SES)
AWS_SES_REGION=us-east-1
FROM_EMAIL=<EMAIL>

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY=your-firebase-private-key
FIREBASE_CLIENT_EMAIL=your-firebase-client-email

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# Health Microservice
HEALTH_MICROSERVICE_URL=http://localhost:5000
HEALTH_MICROSERVICE_API_KEY=your-microservice-api-key

# Expo Push Notifications
EXPO_ACCESS_TOKEN=your-expo-access-token

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-admin-password
```

### 4. Database Setup

#### MongoDB
1. **Install MongoDB locally** or use MongoDB Atlas
2. **Create database**: `appetec`
3. **Start MongoDB service**:
   ```bash
   # On macOS with Homebrew
   brew services start mongodb-community
   
   # On Ubuntu
   sudo systemctl start mongod
   
   # On Windows
   net start MongoDB
   ```

#### Redis
1. **Install Redis locally** or use Redis Cloud
2. **Start Redis service**:
   ```bash
   # On macOS with Homebrew
   brew services start redis
   
   # On Ubuntu
   sudo systemctl start redis-server
   
   # On Windows
   redis-server
   ```

### 5. AWS Services Setup (Optional for Development)

#### S3 Bucket
1. Create an S3 bucket for file storage
2. Configure bucket permissions for public read access
3. Set up CORS policy:
```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": []
  }
]
```

#### SES (Simple Email Service)
1. Verify your domain or email address
2. Move out of sandbox mode for production
3. Configure sending limits

### 6. Firebase Setup (Optional)
1. Create a Firebase project
2. Generate service account credentials
3. Download the service account JSON file
4. Extract required fields for environment variables

## Development Commands

### Start Development Server
```bash
# Start with hot reload
npm run start:dev

# Start with debug mode
npm run start:debug

# Start production build
npm run start:prod
```

### Build Commands
```bash
# Build the application
npm run build

# Clean build
rm -rf dist && npm run build
```

### Testing Commands
```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:cov

# Run e2e tests
npm run test:e2e
```

### Code Quality Commands
```bash
# Lint code
npm run lint

# Format code
npm run format
```

## Database Seeding

### Create Admin User
The application automatically creates an admin user on startup using the credentials from environment variables.

### Seed Sample Data (Optional)
Create a seeding script to populate the database with sample data:

```typescript
// scripts/seed.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { BootstrapService } from '../src/bootstrap.service';

async function seed() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const bootstrapService = app.get(BootstrapService);
  
  // Add your seeding logic here
  await bootstrapService.seedSampleData();
  
  await app.close();
}

seed();
```

## API Documentation

### Swagger UI
Once the server is running, access the API documentation at:
```
http://localhost:4000/api/swagger
```

### API Testing
Use the following tools for API testing:
- **Swagger UI**: Built-in interactive documentation
- **Postman**: Import the API collection
- **curl**: Command line testing
- **HTTPie**: User-friendly command line tool

## Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add your feature description"

# Push to remote
git push origin feature/your-feature-name
```

### 2. Code Review Process
1. Create pull request
2. Ensure all tests pass
3. Code review by team members
4. Merge to main branch

### 3. Testing Strategy
- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test API endpoints
- **E2E Tests**: Test complete user workflows

## Debugging

### VS Code Configuration
Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug NestJS",
      "program": "${workspaceFolder}/src/main.ts",
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector"
    }
  ]
}
```

### Logging
The application uses a custom logger service. Log levels:
- `error`: Error messages
- `warn`: Warning messages
- `log`: General information
- `debug`: Debug information

## Common Issues and Solutions

### 1. MongoDB Connection Issues
```bash
# Check MongoDB status
brew services list | grep mongodb

# Restart MongoDB
brew services restart mongodb-community
```

### 2. Redis Connection Issues
```bash
# Check Redis status
redis-cli ping

# Should return "PONG"
```

### 3. Port Already in Use
```bash
# Find process using port 4000
lsof -i :4000

# Kill the process
kill -9 <PID>
```

### 4. Environment Variables Not Loading
- Ensure `.env` file is in the root directory
- Check for typos in variable names
- Restart the development server

### 5. TypeScript Compilation Errors
```bash
# Clean TypeScript cache
rm -rf dist
rm -rf node_modules/.cache

# Reinstall dependencies
npm ci
```

## Performance Optimization

### Development Tips
1. **Use MongoDB indexes** for frequently queried fields
2. **Implement caching** with Redis for expensive operations
3. **Optimize database queries** to avoid N+1 problems
4. **Use pagination** for large datasets
5. **Implement request validation** to prevent invalid data

### Monitoring
- Monitor API response times
- Track database query performance
- Monitor memory usage
- Set up error tracking (e.g., Sentry)

## Security Considerations

### Development Security
1. **Never commit sensitive data** to version control
2. **Use environment variables** for all configuration
3. **Validate all input data** using DTOs and pipes
4. **Implement rate limiting** to prevent abuse
5. **Use HTTPS** in production
6. **Keep dependencies updated** to avoid vulnerabilities

### Authentication Testing
- Test JWT token expiration
- Verify password hashing
- Test role-based access control
- Validate email verification flow

## Deployment Preparation

### Pre-deployment Checklist
- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Database migrations ready
- [ ] SSL certificates configured
- [ ] Monitoring and logging set up
- [ ] Backup strategy in place
- [ ] Performance testing completed

### Build for Production
```bash
# Create production build
npm run build

# Test production build locally
npm run start:prod
```

This setup guide should get you up and running with the Appetec server development environment. For any issues not covered here, please refer to the project documentation or contact the development team.
