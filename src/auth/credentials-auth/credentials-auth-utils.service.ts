import {
  HttpStatus,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AccessToken, RefreshToken, VerificationToken } from 'models/auth';
import { User } from 'models/user';
import { Model } from 'mongoose';
import { EmailService } from 'src/common/services/email.service';
import { CustomLogger } from 'src/common/services/logger.service';
import { v4 as uuidv4 } from 'uuid';
import { EncryptionService } from 'src/common/services';
import { Request } from 'express';
import { UserRepoService } from 'src/repo/user-repo.service';
import * as argon2 from 'argon2';
import { JwtService } from '@nestjs/jwt';
import { LoginResDto } from './credentials-auth-dto';
import { UserProfileDto } from 'src/user/user/user-dto';
import { ConfigService } from '@nestjs/config';
import { EmailVerificationTemplate } from '../../common/email-templates';

@Injectable()
export class CredentialsAuthUtilsService {
  constructor(
    @InjectModel(VerificationToken.name)
    private readonly VerificationTokenModel: Model<VerificationToken>,

    @InjectModel(RefreshToken.name)
    private readonly refreshTokenModel: Model<RefreshToken>,

    @InjectModel(AccessToken.name)
    private readonly accessTokenModel: Model<AccessToken>,

    private readonly logger: CustomLogger,
    private readonly emailService: EmailService,
    private readonly encryptionService: EncryptionService,
    private readonly userRepo: UserRepoService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {
    this.logger.setContext('CredentialsAuth-Utils');
  }

  async generateVerificationToken(user: User): Promise<VerificationToken> {
    const registartionToken: string = uuidv4();

    const tokenObj: VerificationToken =
      await this.VerificationTokenModel.create({
        userId: user._id.toString(),
        token: registartionToken,
        expiry: Date.now() + Number(process.env.VERIFICATION_TOKEN_EXPIRY),
      });

    return tokenObj;
  }

  async sendVerificationEmail(
    user: User,
    req: Request,
    sub: any,
  ): Promise<void> {
    try {
      const newVerificationToken = await this.generateVerificationToken(user);
      const fromEmail = this.configService.get<string>('AWS_SES_EMAIL')?.trim();
      const emailSubject = 'Verify Your Email to Complete Registration';
      const emailBody =
        'Thank you for signing up with Appetec! To complete your registration, please verify your email address by clicking the link below:';

      if (newVerificationToken != null) {
        const serverUrl =
          process.env.FRONTEND_URL === 'http://localhost:5174'
            ? `${req.protocol}://${req.get('host')}`
            : process.env.FRONTEND_URL;

        const verificationLink = `${serverUrl}/api/verify_user/${newVerificationToken.token}`;

        const data = { link: verificationLink };

        const html = EmailVerificationTemplate.replace(
          '$$email',
          user.name || user.email || 'user',
        )
          .replace('$$data.link', data.link)
          .replace('$$timeLeftMessage', '24 Hours')
          .replace(
            '$$message',
            `Thank you for signing up for <strong>Appetec</strong>. <br />
          Please click the button below to verify your email address and
          securely log in.`,
          );

        const subject: any = sub === null ? emailSubject : sub;

        await this.emailService.sendTextMail({
          fromEmail,
          toEmail: user.email,
          subject,
          textBody: emailBody,
          html,
        });
      }
    } catch (error) {
      throw error;
    }
  }

  async generateEmailUpdateToken(
    user: User,
    newEmail: string,
  ): Promise<VerificationToken> {
    const encryptedNewEmail = this.encryptionService.encrypt(newEmail);
    const registrationToken: string = uuidv4();
    const now = new Date();

    // Fetch all verification tokens for the user that have a new email and expire them
    await this.VerificationTokenModel.updateMany(
      { userId: user._id.toString(), newEmail: { $exists: true } },
      { $set: { isExpired: true, expiry: now } },
    );

    // Create a new verification token
    const tokenObj: VerificationToken =
      await this.VerificationTokenModel.create({
        userId: user._id.toString(),
        token: registrationToken,
        expiry: now.getTime() + Number(process.env.VERIFICATION_TOKEN_EXPIRY),
        newEmail: encryptedNewEmail,
        isExpired: false, // Ensure the new token is active
      });

    return tokenObj;
  }

  async sendEmailUpdateEmail(
    user: User,
    req: Request,
    newEmail: string,
  ): Promise<void> {
    try {
      const emailUpdateToken = await this.generateEmailUpdateToken(
        user,
        newEmail,
      );
      const fromEmail = this.configService.get<string>('AWS_SES_EMAIL')?.trim();

      const subject = 'Verify Your Email to Complete Registration';
      const emailBody =
        'Thank you for signing up with Appetec! To complete your registration, please verify your email address by clicking the link below:';

      if (emailUpdateToken != null) {
        const serverUrl = `${req.protocol}://${req.get('host')}`;

        const emaillUpdateLink = `${serverUrl}/api/email_update/${emailUpdateToken.token}`;

        const data = { link: emaillUpdateLink };

        const html = EmailVerificationTemplate.replace(
          '$$email',
          user.name || user.email || 'user',
        )
          .replace('$$data.link', data.link)
          .replace('$$timeLeftMessage', '24 Hours')
          .replace(
            '$$message',
            `Update your account email by clicking the link below.`,
          );

        await this.emailService.sendTextMail({
          fromEmail,
          toEmail: newEmail,
          subject,
          textBody: emailBody,
          html,
        });
      }
    } catch (error) {
      throw error;
    }
  }

  async validateUser(email: string): Promise<User> {
    const user = await this.userRepo.findUserByEmail(email, false);

    if (!user) {
      throw new NotFoundException(
        "This account doesn't seem to exist. Please sign up to get started!",
      );
    }

    return user;
  }

  async verifyAccountPassword(user: User, password: string): Promise<void> {
    const isPasswordMatched = await argon2.verify(user.password, password);

    if (!isPasswordMatched) {
      throw new UnauthorizedException('Invalid email or password.');
    }
  }

  async generateRefreshToken(user: User): Promise<RefreshToken> {
    const payload = {
      email: user.email,
      user: {
        id: user.id,
        name: user.name,
      },
    };

    const jwtRefreshToken = this.jwtService.sign(payload, {
      expiresIn: process.env.JWT_REFRESH_TOKEN_LIFETIME,
      subject: user.email,
      algorithm: 'HS512',
      secret: process.env.JWT_REFRESH_TOKEN_SECRET as string,
    });

    const expiryInMs = new Date(
      Date.now() + Number(process.env.REFRESH_TOKEN_EXPIRY),
    );

    const newRefreshToken = await this.refreshTokenModel.create({
      token: jwtRefreshToken,
      expiry: expiryInMs,
      userId: user.id,
    });

    return newRefreshToken;
  }

  async generateAccessToken(
    user: User,
    refreshToken: RefreshToken,
  ): Promise<{
    token: string;
    expiryInMs: Date;
  }> {
    const payload = {
      email: user.email,
      user: {
        id: user.id,
        name: user.name,
      },
    };

    const jwtAccessToken = this.jwtService.sign(payload, {
      expiresIn: process.env.JWT_ACCESS_TOKEN_LIFETIME,
      subject: user.email,
      algorithm: 'HS512',
      secret: process.env.JWT_ACCESS_TOKEN_SECRET as string,
    });

    const expiryInMs = new Date(
      Date.now() + Number(process.env.ACCESS_TOKEN_EXPIRY),
    );

    await this.accessTokenModel.create({
      token: jwtAccessToken,
      expiry: expiryInMs,
      userId: user.id,
      refreshTokenId: refreshToken.id,
    });

    return { token: jwtAccessToken, expiryInMs };
  }

  sendLoginResponse(
    user: User,
    accessToken: string,
    expiry: Date,
  ): LoginResDto {
    const userProfile = UserProfileDto.transform(user);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'You have logged in successfully!',
      accessToken,
      expiry,
      user: userProfile,
    } as LoginResDto;
  }
}
