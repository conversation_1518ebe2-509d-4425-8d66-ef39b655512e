import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AppPermission, User, USER_APP_PERMISSIONS } from 'models/user';
import { Model } from 'mongoose';
import {
  forgotPassReqDTO,
  forgotPassResDTO,
  LoginReqDto,
  LoginResDto,
  LogoutResDto,
  RegisterReqDto,
  RegisterResDto,
  ResendVerificationEmailResDTO,
  ResetPassReqDTO,
  ResetPassResDTO,
} from './credentials-auth-dto';
import { CustomLogger } from 'src/common/services/logger.service';
import { AccessToken, RefreshToken, VerificationToken } from 'models/auth';
import * as argon2 from 'argon2';
import { Request } from 'express';
import { UserRepoService } from 'src/repo/user-repo.service';
import { NotificationToken } from 'models/notificationToken';
import * as moment from 'moment-timezone';
import { UserDataInterface } from './interfaces';
import { UserBootstrapService } from './user-register-bootstrap.service';
import { CredentialsAuthUtilsService } from './credentials-auth-utils.service';
import { isEmail } from 'validator';
import {
  CustomConfigService,
  EmailService,
  EncryptionService,
} from 'src/common/services';

@Injectable()
export class CredentialsAuthService {
  constructor(
    private readonly userBootstrapService: UserBootstrapService,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,

    @InjectModel(NotificationToken.name)
    private readonly notificationTokenModel: Model<NotificationToken>,

    @InjectModel(AccessToken.name)
    private readonly accessTokenModel: Model<AccessToken>,

    @InjectModel(RefreshToken.name)
    private readonly refreshTokenModel: Model<RefreshToken>,

    @InjectModel(AppPermission.name)
    private readonly appPermissionModel: Model<AppPermission>,

    @InjectModel(VerificationToken.name)
    private readonly VerificationTokenModel: Model<VerificationToken>,

    private readonly logger: CustomLogger,
    private readonly credentialsAuthUtilsService: CredentialsAuthUtilsService,
    private readonly encryptionService: EncryptionService,
    private readonly userRepo: UserRepoService,
    private readonly emailService: EmailService,
  ) {
    this.logger.setContext('CredentialsAuth');
  }

  async register(
    registerData: RegisterReqDto,
    req: Request,
  ): Promise<RegisterResDto> {
    const { name, email, password, acceptTerms, timeZone } = registerData;

    if (timeZone && !moment.tz.zone(timeZone)) {
      throw new BadRequestException('Invalid Time Zone provided !!');
    }

    if (!acceptTerms) {
      throw new BadRequestException(
        'To proceed, please agree to our terms and conditions.',
      );
    }

    try {
      const user = await this.userRepo.findUserByEmail(email, false);

      if (user) {
        if (user.isDeleted) {
          throw new BadRequestException(
            'This account has been deactivated by the admin as per your request. Contact the admin for reactivation.',
          );
        } else {
          throw new BadRequestException(
            'An account with this email already exists.',
          );
        }
      }

      const applePermission = await this.appPermissionModel.create({
        app_permission: USER_APP_PERMISSIONS.APPLE,
      });

      const googlePermission = await this.appPermissionModel.create({
        app_permission: USER_APP_PERMISSIONS.GOOGLE,
      });

      const userData: UserDataInterface = {
        name,
        email,
        password,
        acceptTerms,
        app_permissions: [applePermission._id, googlePermission._id],
      };

      if (timeZone) {
        userData.timeZone = timeZone;
      }

      const newUser = await this.userModel.create({
        ...userData,
      });

      newUser.email = email;

      this.credentialsAuthUtilsService.sendVerificationEmail(
        newUser,
        req,
        null,
      );

      if (timeZone) {
        this.userBootstrapService.createUserBootstrapReminders(
          newUser,
          timeZone,
        );
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Registration complete! Verification email sent.',
      };
    } catch (error) {
      throw error;
    }
  }

  async logout(authHeader: string, deviceId: string): Promise<LogoutResDto> {
    if (!authHeader) {
      throw new UnauthorizedException(
        'Access token is missing. Please log in again.',
      );
    }

    if (!deviceId) {
      throw new BadRequestException('Please provide device Id to logOut !!');
    }

    const authorization = authHeader.split(' ')[1];

    // Find access token details
    const accessToken = await this.accessTokenModel.findOne({
      token: authorization,
    });

    if (!accessToken) {
      throw new UnauthorizedException(
        'Invalid access token. Please log in again.',
      );
    }

    // Get userId from access token
    const userId = accessToken.userId;

    // Set isActive and isNotificationActive to false
    const updatedNotificatIonToken =
      await this.notificationTokenModel.findOneAndUpdate(
        { userId, deviceId },
        {
          isNotificationActive: false,
          isActive: false,
        },
        { new: true, runValidators: true },
      );

    if (!updatedNotificatIonToken) {
      throw new BadRequestException('Invalid notification token provided !!');
    }

    // Invalidate access token for same device
    await this.accessTokenModel.updateOne(
      { token: authorization },
      { expiry: Date.now(), isExpired: true },
    );

    // Invalidate refresh token for same device
    await this.refreshTokenModel.updateOne(
      { _id: accessToken.refreshTokenId },
      { expiry: Date.now(), isExpired: true },
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'You have logged out successfully!',
    };
  }

  async login(loginData: LoginReqDto): Promise<LoginResDto> {
    try {
      const { email, password } = loginData;

      const user = await this.credentialsAuthUtilsService.validateUser(email);

      if (!user.isEmailVerified) {
        throw new ForbiddenException(
          'Please verify your account first before login.',
        );
      }

      await this.credentialsAuthUtilsService.verifyAccountPassword(
        user,
        password,
      );

      const refreshTokenData =
        await this.credentialsAuthUtilsService.generateRefreshToken(user);
      const accessTokenData =
        await this.credentialsAuthUtilsService.generateAccessToken(
          user,
          refreshTokenData,
        );

      return this.credentialsAuthUtilsService.sendLoginResponse(
        user,
        accessTokenData.token,
        accessTokenData.expiryInMs,
      );
    } catch (error) {
      throw error;
    }
  }

  async validateVerificationToken(token: string) {
    try {
      const userVerificationToken = await this.VerificationTokenModel.findOne({
        token,
        isExpired: false,
      });

      if (!userVerificationToken) {
        throw new BadRequestException('Token not found !!');
      }

      if (userVerificationToken && userVerificationToken.expiry >= new Date()) {
        await this.VerificationTokenModel.findOneAndUpdate(
          {
            token,
            isExpired: false,
          },
          {
            isExpired: true,
          },
        );

        await this.userRepo.findUserByIdAndUpdate(
          userVerificationToken.userId.toString(),
          {
            isEmailVerified: true,
            isActive: true,
          },
        );

        return true;
      } else {
        return false;
      }
    } catch (error) {
      this.logger.error(error);
      return false;
    }
  }

  async validateEmailUpdateToken(emailUpdateToken: string): Promise<boolean> {
    try {
      const userEmailUpdateToken = await this.VerificationTokenModel.findOne({
        token: emailUpdateToken,
        isExpired: false,
      });

      if (!userEmailUpdateToken) {
        throw new BadRequestException('Token not found !!');
      }

      if (
        userEmailUpdateToken &&
        userEmailUpdateToken.expiry >= new Date() &&
        userEmailUpdateToken.newEmail
      ) {
        await this.VerificationTokenModel.findOneAndUpdate(
          {
            token: emailUpdateToken,
            isExpired: false,
            newEmail: userEmailUpdateToken.newEmail,
          },
          {
            isExpired: true,
          },
        );

        const decryptedEmail = this.encryptionService.decrypt(
          userEmailUpdateToken.newEmail,
        ); // returns email decrypted string

        if (!isEmail(decryptedEmail)) {
          throw new BadRequestException('Invalid Email Provided.');
        }

        await this.userRepo.findUserByIdAndUpdate(
          userEmailUpdateToken.userId.toString(),
          {
            email: decryptedEmail,
          },
        );

        return true;
      } else {
        return false;
      }
    } catch (error) {
      this.logger.error(error);
      return false;
    }
  }

  async sendForgotPassEmail(
    forgotPassData: forgotPassReqDTO,
    req: Request,
  ): Promise<forgotPassResDTO> {
    const { email } = forgotPassData;

    try {
      const user = await this.userRepo.findUserByEmail(email);

      if (!user) {
        throw new BadRequestException('User does not exist');
      }

      // Check if the user already has a valid token and expire it
      const existingToken = await this.VerificationTokenModel.findOne({
        userId: user.id,
        isExpired: false,
      });

      if (existingToken) {
        await this.VerificationTokenModel.findOneAndUpdate(
          { userId: user.id, isExpired: false },
          { isExpired: true },
        );
      }

      // Generate a new verification token
      const newVerificationToken =
        await this.credentialsAuthUtilsService.generateVerificationToken(user);

      const { subject, emailBody, fromEmail, ForgotPasswordTemplate } =
        CustomConfigService.PROPERTIES.forgotPasswordEmail;

      const serverUrl =
        process.env.FRONTEND_URL === 'http://localhost:5174'
          ? `${req.protocol}://${req.get('host')}`
          : process.env.FRONTEND_URL;

      const forgotPassLink = `${serverUrl}/api/forget_password/${newVerificationToken.token}`;
      const data = { link: forgotPassLink };

      const html = ForgotPasswordTemplate.replace(
        '$$email',
        user.name || user.email,
      )
        .replace('$$data.link', data.link)
        .replace('$$timeLeftMessage', '24 Hours');

      await this.emailService.sendTextMail({
        fromEmail,
        toEmail: user.email,
        subject,
        textBody: emailBody,
        html,
      });

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Password reset email sent! Check your inbox for instructions.',
      };
    } catch (error) {
      throw error;
    }
  }

  async validateForgotPassToken(token: string) {
    try {
      const userVerificationToken = await this.VerificationTokenModel.findOne({
        token,
        isExpired: false,
      });

      if (!userVerificationToken) {
        throw new BadRequestException('Token not found !!');
      }

      if (userVerificationToken && userVerificationToken.expiry >= new Date()) {
        await this.VerificationTokenModel.findOneAndUpdate(
          {
            token,
            isExpired: false,
          },
          {
            isExpired: true,
          },
        );

        return true;
      } else {
        return false;
      }
    } catch (error) {
      this.logger.error(error);
      return false;
    }
  }

  async resetPassword(newUserData: ResetPassReqDTO): Promise<ResetPassResDTO> {
    try {
      const userVerificationToken = await this.VerificationTokenModel.findOne({
        token: newUserData.token,
      });

      if (!userVerificationToken) {
        throw new BadRequestException('Invalid or expired token');
      }

      const user = await this.userModel.findById(userVerificationToken.userId);

      if (!user) {
        throw new BadRequestException('User not found');
      }

      const hashedPassword = await argon2.hash(newUserData.newPass);

      await this.userRepo.findUserByIdAndUpdate(user._id.toString(), {
        password: hashedPassword,
      });

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'User password updated successfully',
      };
    } catch (error) {
      throw error;
    }
  }

  async resendVerificationEmail(
    user: User,
    req: Request,
  ): Promise<ResendVerificationEmailResDTO> {
    if (user.isEmailVerified) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'Account already verified',
      };
    }

    const userVerificationToken = await this.VerificationTokenModel.findOne({
      userId: user.id,
      isExpired: false,
    });

    if (userVerificationToken && userVerificationToken.expiry >= new Date()) {
      await this.VerificationTokenModel.findOneAndUpdate(
        {
          userId: user.id,
          isExpired: false,
        },
        {
          isExpired: true,
        },
      );
    }
    const sub: any = 'Resend Account Verification email';
    await this.credentialsAuthUtilsService.sendVerificationEmail(
      user,
      req,
      sub,
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Verification OTP re-sent successfully!',
    };
  }
}
