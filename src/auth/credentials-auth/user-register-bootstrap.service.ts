import { Injectable } from '@nestjs/common';
import { REMINDER_TIME_PERIOD } from 'models/reminders';
import { User } from 'models/user';
import { CreateReminderReqDTO } from 'src/user/reminders/dto';
import { REMINDER_CATEGORY_ENUM } from 'src/user/reminders/reminders-utils.service';
import { RemindersService } from 'src/user/reminders/reminders.service';

@Injectable()
export class UserBootstrapService {
  constructor(private readonly remindersService: RemindersService) {}

  async createUserBootstrapReminders(user: User, timeZone: string) {
    const BootStrapReminderData: CreateReminderReqDTO[] = [
      {
        category: REMINDER_CATEGORY_ENUM.FOOD,
        frontend_screen_url: 'appetec://reminders',
        label: 'Breakfast',
        sound: {
          android: 'default',
          ios: 'default',
        },
        timeZone,
        userTime: {
          hour: 9,
          minute: 0,
          period: REMINDER_TIME_PERIOD.AM,
        },
      },

      {
        category: REMINDER_CATEGORY_ENUM.FOOD,
        frontend_screen_url: 'appetec://reminders',
        label: 'Lunch',
        sound: {
          android: 'default',
          ios: 'default',
        },
        timeZone,
        userTime: {
          hour: 1,
          minute: 0,
          period: REMINDER_TIME_PERIOD.PM,
        },
      },

      {
        category: REMINDER_CATEGORY_ENUM.FOOD,
        frontend_screen_url: 'appetec://reminders',
        label: 'Dinner',
        sound: {
          android: 'default',
          ios: 'default',
        },
        timeZone,
        userTime: {
          hour: 7,
          minute: 0,
          period: REMINDER_TIME_PERIOD.PM,
        },
      },

      {
        category: REMINDER_CATEGORY_ENUM.WORKOUT,
        frontend_screen_url: 'appetec://reminders',
        label: 'Walking',
        sound: {
          android: 'default',
          ios: 'default',
        },
        timeZone,
        userTime: {
          hour: 7,
          minute: 0,
          period: REMINDER_TIME_PERIOD.AM,
        },
      },
    ];

    await Promise.all(
      BootStrapReminderData.map(async (singleReminderData) => {
        await this.remindersService.createReminder(
          singleReminderData,
          user._id.toString(),
        );
      }),
    );
  }
}
