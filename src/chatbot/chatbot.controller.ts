import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import {
  AskChatbotQueryReqDTO,
  AskChatbotQueryResDTO,
  GetAllSessionMessagesResDTO,
} from './dtos';
import { ChatbotService } from './chatbot.service';
import { AuthGuard } from 'src/middlewares';
import { GetAllSessionMessagesQueryInterface } from './interfaces';

@ApiTags('Chatbot')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('chatbot')
export class ChatbotController {
  constructor(private readonly chatbotService: ChatbotService) {}

  @ApiResponse({
    status: 200,
    description: 'Chat bot Query Success Response',
    type: AskChatbotQueryResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post()
  async AskChatbotQuery(
    @Req() req: Request,
    @Body() reqBody: AskChatbotQueryReqDTO,
  ) {
    const user = req['user'];

    return this.chatbotService.askChatBotQuery(user, reqBody);
  }

  @ApiResponse({
    status: 200,
    description: 'Get All Messages in a Session Response',
    type: GetAllSessionMessagesResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/:sessionId')
  async GetAllSessionMessages(
    @Param('sessionId') sessionId: string,
    @Query() queryFilters: GetAllSessionMessagesQueryInterface,
    @Req() req: Request,
  ) {
    const user = req['user'];

    return this.chatbotService.getAllSessionMessages(
      sessionId,
      queryFilters,
      user,
    );
  }
}
