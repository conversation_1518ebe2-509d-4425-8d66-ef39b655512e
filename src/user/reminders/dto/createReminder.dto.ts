import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsObject,
  IsString,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { BaseResponse } from 'src/utils/responses';
import { ReminderDTO } from './reminder.dto';
import { Transform, Type } from 'class-transformer';
import { ReminderTimeDTO } from './userTime.dto';
import { ReminderSoundDTO } from './reminderSound.dto';

export class CreateReminderReqDTO {
  @ApiProperty({ example: 'health', description: 'Category of the reminder' })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.trim().charAt(0).toUpperCase() + value.trim().slice(1);
    }
    return value;
  })
  category: string;

  @ApiProperty({
    example: 'Morning workout',
    description: 'Label for the reminder',
  })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  label: string;

  @ApiProperty({
    example: 'app://reminder',
    description: 'Frontend screen URL',
  })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  frontend_screen_url: string;

  @ApiProperty({
    example: 'chime.mp3',
    description: 'Sound file for the reminder',
  })
  @IsObject()
  @IsNotEmpty()
  sound: ReminderSoundDTO;

  @ApiProperty({ description: 'User-defined time for the reminder' })
  @ValidateNested()
  @Type(() => ReminderTimeDTO)
  @IsNotEmpty()
  userTime: ReminderTimeDTO;

  @ApiProperty({
    example: 'America/New_York',
    description: 'Timezone for the reminder',
  })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  timeZone: string;

  @ApiPropertyOptional({
    description: 'Additional metadata for the reminder',
    type: Object,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CreateReminderResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Reminder created successfully',
  })
  msg: string;

  @ApiProperty({ description: 'Created reminder details', type: ReminderDTO })
  data: ReminderDTO;
}
