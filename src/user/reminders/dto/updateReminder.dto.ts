import { ApiProperty } from '@nestjs/swagger';
import {
  ValidateNested,
  IsString,
  IsObject,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { BaseResponse } from 'src/utils/responses';
import { ReminderDTO } from './reminder.dto';
import { ReminderTimeDTO } from './userTime.dto';
import { ReminderSoundDTO } from './reminderSound.dto';

export class UpdateReminderReqDTO {
  @ApiProperty({ description: 'label of the reminder' })
  @IsString()
  @IsOptional()
  label: string;

  @ApiProperty({
    description: 'Time for the reminder',
  })
  @ValidateNested()
  @Type(() => ReminderTimeDTO)
  @IsOptional()
  userTime: ReminderTimeDTO;

  @ApiProperty({
    description: 'Sound string for the reminder',
  })
  @IsObject()
  @IsOptional()
  sound: ReminderSoundDTO;

  @ApiProperty({
    description: 'Additional metadata related to the reminder',
    example: {
      location: 'Home',
      priority: 'High',
      customNote: 'Take before gym',
    },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateReminderResDTO extends BaseResponse {
  @ApiProperty({ description: 'Response message' })
  msg: string;

  @ApiProperty({
    description: 'Updated reminder data',
    type: () => ReminderDTO,
  })
  data: ReminderDTO;
}
