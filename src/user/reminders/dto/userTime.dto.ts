import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, <PERSON>NotEmpt<PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { REMINDER_TIME_PERIOD, ReminderTime } from 'models/reminders';

export class ReminderTimeDTO {
  @ApiProperty({ example: 10, description: 'Hour of the reminder (0-12)' })
  @Max(12)
  @Min(0)
  @IsNotEmpty()
  hour: number;

  @ApiProperty({ example: 30, description: 'Minute of the reminder (0-59)' })
  @Max(59)
  @Min(0)
  @IsNotEmpty()
  minute: number;

  @ApiProperty({
    example: REMINDER_TIME_PERIOD.AM,
    enum: REMINDER_TIME_PERIOD,
    description: 'AM or PM',
  })
  @IsEnum(REMINDER_TIME_PERIOD)
  @IsNotEmpty()
  period: REMINDER_TIME_PERIOD;

  static transform(object: ReminderTime): ReminderTimeDTO {
    const transformedObj = new ReminderTimeDTO();

    transformedObj.hour = object.hour;
    transformedObj.minute = object.minute;
    transformedObj.period = object.period as REMINDER_TIME_PERIOD;

    return transformedObj;
  }
}
