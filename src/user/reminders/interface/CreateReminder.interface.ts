import { ReminderSoundDTO } from '../dto/reminderSound.dto';
import { ReminderTimeDTO } from '../dto/userTime.dto';

export interface CreateReminderDataInterface {
  userId: string;
  categoryId: string;
  categoryString: string;
  label: string;
  GMT_Time: ReminderTimeDTO;
  frontend_screen_url: string;
  sound: ReminderSoundDTO;
  userTime: ReminderTimeDTO;
  timeZone: string;
  metadata?: Record<string, any>;
}
