import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  REMINDER_TIME_PERIOD,
  ReminderCategory,
  ReminderSettings,
} from 'models/reminders';
import { Model, Types } from 'mongoose';
import {
  ReminderActivityMetaDataDTO,
  ReminderDeviceMetaDataDTO,
  ReminderFoodMetaDataDTO,
} from './dto';
import { UpdateReminderReqDTO } from './dto/updateReminder.dto';
import { UpdateReminderPayloadInterface } from './interface';
import { validate } from 'class-validator';
import { Recipes } from 'models/recipe';
import { plainToClass } from 'class-transformer';
import { ReminderTimeDTO } from './dto/userTime.dto';
import { UtilsService } from 'src/common/services';
import { RecipesNutritionByQuantityDTO } from '../recipe/dto';

export enum REMINDER_CATEGORY_ENUM {
  FOOD = 'Food',
  WORKOUT = 'Workout',
  DEVICE = 'Device',
}

@Injectable()
export class RemindersUtilsService {
  constructor(
    private readonly utilsService: UtilsService,

    @InjectModel(Recipes.name) private readonly recipeModel: Model<Recipes>,

    @InjectModel(ReminderCategory.name)
    private reminderCategoryModel: Model<ReminderCategory>,
  ) {}

  getMetadataDto(
    category: string,
    metadata: any,
  ):
    | ReminderFoodMetaDataDTO
    | ReminderActivityMetaDataDTO
    | ReminderDeviceMetaDataDTO {
    switch (category) {
      case REMINDER_CATEGORY_ENUM.FOOD:
        return plainToClass(ReminderFoodMetaDataDTO, metadata);
      case REMINDER_CATEGORY_ENUM.WORKOUT:
        return plainToClass(ReminderActivityMetaDataDTO, metadata);
      case REMINDER_CATEGORY_ENUM.DEVICE:
        return plainToClass(ReminderDeviceMetaDataDTO, metadata);
      default:
        throw new BadRequestException('Invalid reminder category');
    }
  }

  async validateMetadata(
    metadataDto:
      | ReminderFoodMetaDataDTO
      | ReminderActivityMetaDataDTO
      | ReminderDeviceMetaDataDTO,
  ): Promise<void> {
    const validationErrors = await validate(metadataDto, {
      whitelist: true,
      forbidNonWhitelisted: true,
    });

    if (validationErrors.length > 0) {
      // Get the first validation error
      const firstError = validationErrors[0];

      // Extract the first error message
      let errorMessage = 'Validation error';

      if (
        firstError.children[0].children &&
        firstError.children[0].children.length > 0
      ) {
        const nestedError = firstError.children[0].children[0];

        if (nestedError.constraints) {
          errorMessage = Object.values(nestedError.constraints)[0];
        }
      } else if (firstError.children && firstError.children.length > 0) {
        const nestedError = firstError.children[0];
        if (nestedError.constraints) {
          errorMessage = Object.values(nestedError.constraints)[0];
        }
      } else if (firstError.constraints) {
        errorMessage = Object.values(firstError.constraints)[0];
      }

      throw new BadRequestException(
        `Invalid metadata provided : ${errorMessage}`,
      );
    }
  }

  async validateFoodReminderMetadata(
    metadata: ReminderFoodMetaDataDTO,
    userId: string,
  ): Promise<ReminderFoodMetaDataDTO> {
    const { meals } = metadata;

    const invalidIds = meals.filter(
      (item) => !Types.ObjectId.isValid(item.recipeId),
    );

    if (invalidIds.length > 0) {
      throw new BadRequestException(
        `Invalid Recipe ID(s) provided: ${invalidIds.map((item) => item.recipeId).join(', ')}`,
      );
    }

    const uniqueRecipes = await this.recipeModel.find({
      _id: { $in: meals.map((item) => new Types.ObjectId(item.recipeId)) },
      $or: [{ author: userId }, { author: null }],
    });

    // Create a map for quick lookup
    const recipeMap = new Map(
      uniqueRecipes.map((recipe) => [recipe._id.toString(), recipe]),
    );

    // Create a new array with repeated recipes
    const recipes = meals.map((meal) => {
      const recipe = recipeMap.get(meal.recipeId);
      if (!recipe) {
        throw new BadRequestException(
          `Recipe with ID ${meal.recipeId} not found!`,
        );
      }
      return recipe;
    });

    if (recipes.length !== meals.length) {
      throw new BadRequestException(`Some Recipe(s) not found !!`);
    }

    // ... existing code ...
    const updatedMetaData: ReminderFoodMetaDataDTO = {
      ...metadata,
      meals: metadata.meals.map((item) => {
        const recipe = recipes.find(
          (singleRecipe) => singleRecipe._id.toString() === item.recipeId,
        );

        item.title = recipe.title;
        item.thumbnailUrl = recipe.thumbnailUrl;
        item.nutritionByQuantity = RecipesNutritionByQuantityDTO.transform(
          recipe.nutritionByQuantity.find(
            (nutrition) =>
              nutrition.quantity.toString() === item.quantity.toString(),
          ),
        );

        return item;
      }),
    };

    return updatedMetaData;
  }

  prepareUpdatePayload(
    reminder: ReminderSettings,
    updateData: UpdateReminderReqDTO,
  ): UpdateReminderPayloadInterface {
    const updatePayload: UpdateReminderPayloadInterface = {};

    if (updateData.label?.trim()) {
      updatePayload.label = updateData.label.trim();
    }

    if (updateData.sound) {
      updatePayload.sound = updateData.sound;
    }

    if (updateData.metadata) {
      updatePayload.metadata = updateData.metadata;
    }

    if (updateData.userTime) {
      const GMT_Time = this.convertUserTimeToGMT(
        updateData.userTime,
        reminder.timeZone,
      );

      updatePayload.userTime = updateData.userTime;
      updatePayload.GMT_Time = GMT_Time;
    }

    return updatePayload;
  }

  async getOrCreateCategory(
    category: string,
    userId: string,
  ): Promise<{ categoryString: string; categoryId: string }> {
    const existingCategory = await this.reminderCategoryModel.findOne({
      userId,
      name: category,
      isDeleted: false,
      isActive: true,
    });

    if (existingCategory) {
      return {
        categoryId: existingCategory.id,
        categoryString: existingCategory.name,
      };
    }

    const newCategory = await this.reminderCategoryModel.create({
      userId,
      name: category,
    });

    return { categoryId: newCategory.id, categoryString: newCategory.name };
  }

  convertUserTimeToGMT(
    userTime: ReminderTimeDTO,
    timeZone: string,
  ): ReminderTimeDTO {
    const { gmtHour, gmtMinute, gmtPeriod } = this.utilsService.convertToGMT(
      userTime.hour,
      userTime.minute,
      userTime.period,
      timeZone,
    );

    const GMT_Time = new ReminderTimeDTO();
    GMT_Time.hour = gmtHour;
    GMT_Time.minute = gmtMinute;
    GMT_Time.period = gmtPeriod as REMINDER_TIME_PERIOD;

    return GMT_Time;
  }
}
