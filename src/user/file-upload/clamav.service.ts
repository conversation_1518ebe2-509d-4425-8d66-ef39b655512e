import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as clamav from 'clamav.js';

@Injectable()
export class ClamAVService {
  private readonly CLAMAV_PORT: number;
  private readonly CLAMAV_HOST: string;

  constructor(private readonly configService: ConfigService) {
    this.CLAMAV_PORT = Number(this.configService.get('CLAMAV_PORT') || 3310);
    this.CLAMAV_HOST = this.configService.get('CLAMAV_HOST') || 'localhost';
  }

  async scanBuffer(buffer: Buffer): Promise<boolean> {
    return new Promise((resolve, reject) => {
      clamav.ping(this.CLAMAV_PORT, this.CLAMAV_HOST, 1000, (err) => {
        if (err) return reject(new Error('ClamAV not available'));

        clamav.scanBuffer(
          buffer,
          this.CLAMAV_PORT,
          this.CLAMAV_HOST,
          (err, object, malicious) => {
            if (err) return reject(err);
            resolve(malicious);
          },
        );
      });
    });
  }
}
