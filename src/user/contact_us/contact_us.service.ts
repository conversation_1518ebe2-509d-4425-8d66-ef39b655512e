import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  CONTACTUS_ATTACHMENTS_TYPES,
  ContactUsQuery,
} from 'models/contact-us-query';
import { User } from 'models/user';
import { Model } from 'mongoose';
import { AwsS3Service } from 'src/third-party/aws';
import {
  ContactUsQueryDTO,
  PostContactUsQueryReqDTO,
  PostContactUsQueryResDTO,
} from './dto';
import {
  CustomConfigService,
  EmailService,
  EncryptionService,
} from 'src/common/services';

@Injectable()
export class ContactUsService {
  constructor(
    private readonly s3Service: AwsS3Service,
    private readonly emailServce: EmailService,
    private readonly encryptionService: EncryptionService,

    @InjectModel(ContactUsQuery.name)
    private contactUsQueryModel: Model<ContactUsQuery>,
  ) {}

  async postContactUsQuery(
    user: User,
    postData: PostContactUsQueryReqDTO,
    attachments: Express.Multer.File[] = [],
  ): Promise<PostContactUsQueryResDTO> {
    const { category, description } = postData; // data will be validated via dtos

    const createData: any = {
      category,
      description,
      userId: user._id,
    };

    if (attachments.length > 0) {
      const uploadedUrls = await Promise.all(
        attachments.map(async (file) => {
          const type = file.mimetype.split('/')[0];

          if (type.toUpperCase() in CONTACTUS_ATTACHMENTS_TYPES) {
            return {
              type,
              url: (await this.s3Service.uploadFile(file)).Location,
            };
          } else {
            throw new BadRequestException(`${type} files are not supported.`);
          }
        }),
      );

      createData.attachments = uploadedUrls;
    }

    const new_query = await this.contactUsQueryModel.create(createData);

    const updatedQuery: any = { ...new_query.toObject(), user };

    const {
      subject,
      emailBody,
      fromEmail,
      contactUsQueryConfirmationEmailTemplate,
    } = CustomConfigService.PROPERTIES.contactUsQueryConfirmationEmail;

    const html = contactUsQueryConfirmationEmailTemplate.replace(
      '$$name',
      user.name,
    );

    await this.emailServce.sendTextMail({
      fromEmail,
      toEmail: user.email,
      subject,
      html,
      textBody: emailBody,
    });

    const resp = ContactUsQueryDTO.transform(updatedQuery);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }
}
