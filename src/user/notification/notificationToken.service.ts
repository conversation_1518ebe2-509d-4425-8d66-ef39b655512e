import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { User } from 'models/user';
import {
  NotificationCounter,
  NotificationToken,
} from 'models/notificationToken';
import {
  CreateNotificationTokenReqDTO,
  GetUserNotificationTokensResDTO,
  ToggleNotificationStatusReqDTO,
  ToggleNotificationStatusResDTO,
} from './notification-dto';
import { CreateNotificationTokenResDTO } from './notification-dto';
import { NotificationTokenDto } from './notification-dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(NotificationToken.name)
    private readonly notificationTokenModel: Model<NotificationToken>,

    @InjectModel(NotificationCounter.name)
    private readonly notificationCounterModel: Model<NotificationCounter>,
  ) {}

  async createUserNotificationToken(
    user: User,
    notificationTokenData: CreateNotificationTokenReqDTO,
  ): Promise<CreateNotificationTokenResDTO> {
    const { deviceId, deviceType, notificationToken, osType } =
      notificationTokenData;

    // Find any existing notification token for the same device but a different user
    const previousUserToken = await this.notificationTokenModel.findOne({
      deviceId,
      userId: { $ne: user._id }, // Ensure it's a different user
    });

    // Deactivate previous user's device and notification
    if (previousUserToken) {
      previousUserToken.isActive = false;
      previousUserToken.isNotificationActive = false;
      await previousUserToken.save();
    }

    // Check if this user already has a notification token
    const existingNotificationToken = await this.notificationTokenModel.findOne(
      {
        userId: user._id,
        deviceId,
      },
    );

    // Create a new notification token for the current user if not token found
    if (!existingNotificationToken) {
      const newNotificationToken = await this.notificationTokenModel.create({
        deviceId,
        deviceType,
        notificationToken,
        osType,
        userId: user._id,
      });

      await this.notificationCounterModel.create({
        deviceId,
        userId: user._id,
        notificationTokenId: newNotificationToken._id,
      });

      const resp = NotificationTokenDto.transform(newNotificationToken);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'New Notification Token created successfully!',
        data: resp,
      };
    }

    const existingNotificationCounter =
      await this.notificationCounterModel.findOne({
        deviceId,
        userId: user._id,
        notificationTokenId: existingNotificationToken._id,
      });

    if (existingNotificationCounter) {
      existingNotificationCounter.counter = 0;
      existingNotificationCounter.save();
    }

    // Update the existing notification token for the same user
    existingNotificationToken.notificationToken = notificationToken;
    existingNotificationToken.isActive = true;
    existingNotificationToken.isNotificationActive = true;

    await existingNotificationToken.save();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Notification Token Updated Successfully!',
      data: NotificationTokenDto.transform(existingNotificationToken),
    };
  }

  async toggleNotificationStatus(
    user: User,
    toggleStatusDto: ToggleNotificationStatusReqDTO,
  ): Promise<ToggleNotificationStatusResDTO> {
    const { deviceId, isNotificationActive } = toggleStatusDto;

    const userNotificationTokenData = await this.notificationTokenModel.findOne(
      {
        userId: user._id,
        deviceId,
      },
    );

    if (!userNotificationTokenData) {
      throw new NotFoundException('Notification token not found !!.');
    }

    // Update the isNotificationActive status
    userNotificationTokenData.isNotificationActive = isNotificationActive;
    await userNotificationTokenData.save();

    const resp = NotificationTokenDto.transform(userNotificationTokenData);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Notification status updated successfully',
      data: resp,
    };
  }

  async getUserNotificationTokens(
    user: User,
    deviceId: string,
  ): Promise<GetUserNotificationTokensResDTO> {
    const userNotificationTokenData = await this.notificationTokenModel.findOne(
      {
        userId: user._id,
        deviceId,
      },
    );

    if (!userNotificationTokenData) {
      throw new NotFoundException('Notification token not found !!.');
    }

    const resp = NotificationTokenDto.transform(userNotificationTokenData);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: resp,
    };
  }
}
