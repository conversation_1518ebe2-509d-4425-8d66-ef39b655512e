import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { AppPermission, GOAL_TYPES, User, UserGoal } from 'models/user';
import {
  CompleteUserProfileReqDTO,
  CompleteUserProfileResDTO,
  GetUserProfileResDTO,
  UpdateUserProfileReqDTO,
  UpdateUserProfileResDTO,
  UpdateUserTimeZoneReqDTO,
  UpdateUserTimeZoneResDTO,
  UserProfileDto,
} from './user-dto';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { UserRepoService } from 'src/repo/user-repo.service';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
// import { DeviceService } from '../device/device.service';
import { UserDeviceService } from '../device/device.service';
import { CreateDeviceConnectionReqDTO } from '../device/device-dto';
import { Request } from 'express';
import { CredentialsAuthUtilsService } from 'src/auth/credentials-auth/credentials-auth-utils.service';
import * as moment from 'moment-timezone';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name)
    private readonly userModel: Model<User>,

    @InjectModel(UserGoal.name) private userGoalModel: Model<UserGoal>,

    private readonly userRepo: UserRepoService,
    private readonly credentialsAuthUtilsService: CredentialsAuthUtilsService,
    private readonly userdeviceService: UserDeviceService,
  ) {}

  @InjectModel(AppPermission.name)
  private readonly appPermissionModel!: Model<AppPermission>;

  async getUserProfile(user: User): Promise<GetUserProfileResDTO> {
    const userResp = UserProfileDto.transform(user);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      user: userResp,
    };
  }

  async completeUserProfile(
    user: User,
    completeUserProfileData: CompleteUserProfileReqDTO,
  ): Promise<CompleteUserProfileResDTO> {
    try {
      if (user.isAccountCompleted) {
        throw new BadRequestException('Account is already completed.');
      }

      const {
        age,
        gender,
        height,
        weight,
        diet_preference,
        activity_goal,
        mind_goal,
        physical_goal,
        sleep_goal,
        deviceData,
        deviceUsageLimit,
        app_permissions,
      } = completeUserProfileData;

      const decimalPlaces = (weight.toString().split('.')[1] || '').length;
      if (decimalPlaces > 4) {
        throw new BadRequestException(
          'Weight can have maximum 4 decimal places',
        );
      }

      const goalMap = {
        [GOAL_TYPES.MINDFULNESS.toUpperCase()]: mind_goal,
        [GOAL_TYPES.MOVEMENT.toUpperCase()]: activity_goal,
        [GOAL_TYPES.PHYSICAL.toUpperCase()]: physical_goal,
        [GOAL_TYPES.SLEEP.toUpperCase()]: sleep_goal,
      };

      const goalInstances = Object.entries(goalMap).map(
        ([goalType, goalValue]) =>
          new this.userGoalModel({
            goal_type: GOAL_TYPES[goalType],
            selected_goal: goalValue,
          }),
      );

      const goalData = await Promise.all(
        goalInstances.map((goal) => goal.save()),
      );

      let connectionresp = null;

      if (deviceData) {
        const deviceDtoInstance = plainToInstance(
          CreateDeviceConnectionReqDTO,
          deviceData,
        );

        // Validate the instance
        const errors = await validate(deviceDtoInstance);

        if (errors.length > 0) {
          throw new BadRequestException(
            `${Object.values(errors[0].constraints)}`,
          );
        }

        // Call createDeviceConnection service
        connectionresp = await this.userdeviceService.createDeviceConnection(
          user,
          deviceData,
        );
      }

      if (app_permissions?.length > 0) {
        // Fetch the existing permissions for the user
        const existingPermissions = await this.appPermissionModel.find({
          _id: { $in: user.app_permissions },
        });

        // Map permissions to update only matching ones
        const updatedPermissions = existingPermissions.map((perm) => {
          if (app_permissions.includes(perm.app_permission)) {
            perm.isPermissionAllowed = true;
          }
          return perm.save(); // Save updated permissions
        });

        await Promise.all(updatedPermissions);
      }

      // Prepare the update data
      const completeData: any = {
        age,
        gender,
        height,
        weight,
        diet_preference,
        goals: goalData,
        isAccountCompleted: true,
        deviceUsageLimit,
      };

      const completedUser = await this.userRepo.findUserByIdAndUpdate(
        user._id.toString(),
        completeData,
      );

      const userResp = UserProfileDto.transform(completedUser);

      if (connectionresp != null) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
          msg: 'User Profile Completed !!',
          user: userResp,
          device: connectionresp,
        };
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'User Profile Completed !!',
        user: userResp,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateUserProfile(
    user: User,
    updateUserProfileData: UpdateUserProfileReqDTO,
    req: Request,
  ): Promise<UpdateUserProfileResDTO> {
    const {
      name,
      email: newEmail,
      age,
      height,
      weight,
      diet_preference,
    } = updateUserProfileData;

    const updateData: any = {};
    let responseMessage: string = 'User updated successfully !!';
    let isEmailChanged: boolean = false;

    if (name) updateData.name = name;

    if (age) updateData.age = age;

    if (height) updateData.height = height;

    if (weight) {
      const decimalPlaces = (weight.toString().split('.')[1] || '').length;

      if (decimalPlaces > 4) {
        throw new BadRequestException(
          'Weight can have maximum 4 decimal places',
        );
      }

      updateData.weight = weight;
    }

    if (diet_preference) updateData.diet_preference = diet_preference;

    if (newEmail && newEmail !== user.email) {
      const existingUser = await this.userRepo.findUserByEmail(newEmail);

      if (existingUser) {
        throw new BadRequestException(
          'Email cannot be updated as this email is already in use.',
        );
      }

      isEmailChanged = true;
      responseMessage =
        'User updated successfully, A verification link has been sent to your email.';
    }

    const updatedUser = await this.userRepo.findUserByIdAndUpdate(
      user._id.toString(),
      updateData,
    );

    const userResp = UserProfileDto.transform(updatedUser);

    if (isEmailChanged) {
      await this.credentialsAuthUtilsService.sendEmailUpdateEmail(
        user,
        req,
        newEmail,
      );

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: responseMessage,
        user: userResp,
      };
    } else {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: responseMessage,
        user: userResp,
      };
    }
  }

  async updateUserTimeZone(
    user: User,
    updateData: UpdateUserTimeZoneReqDTO,
  ): Promise<UpdateUserTimeZoneResDTO> {
    const { timeZone } = updateData;

    if (timeZone && !moment.tz.zone(timeZone)) {
      throw new BadRequestException('Invalid Time Zone provided !!');
    }

    await this.userModel.findByIdAndUpdate(
      user._id,
      { timeZone },
      { new: true, runValidators: true },
    );

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'User Time Zone Updated Successfully !!',
    };
  }

  async getUserStatus(user: User): Promise<GetUserProfileResDTO> {
    const transformedUser = UserProfileDto.transform(user);

    // Keep only required fields
    const minimalUser = {
      isEmailVerified: transformedUser.isEmailVerified,
      isAccountCompleted: transformedUser.isAccountCompleted,
      isDeleted: transformedUser.isDeleted,
    } as Partial<UserProfileDto>;

    return {
      error: false,
      statusCode: HttpStatus.OK,
      user: minimalUser as UserProfileDto,
    };
  }
}
