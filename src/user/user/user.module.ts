import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { User, UserGoal, UserGoalSchema, UserSchema } from 'models/user';
import { MongooseModule } from '@nestjs/mongoose';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from 'src/redis/redis.module';
import { RepoModule } from 'src/repo/repo.module';
// import { DeviceModule } from '../device/device.module';
import {
  AppPermission,
  AppPermissionSchema,
} from 'models/user/user_app_permissions.schema';
import { UserDeviceModule } from '../device/device.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: UserGoal.name, schema: UserGoalSchema },
      { name: AppPermission.name, schema: AppPermissionSchema },
    ]),
    CommonModule,
    AuthModule,
    UserDeviceModule,
    RedisModule,
    RepoModule,
  ],
  providers: [UserService],
  controllers: [UserController],
})
export class UserModule {}
