import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  IsNotEmpty,
  IsEnum,
  IsOptional,
  IsNumber,
  IsArray,
  ValidateIf,
} from 'class-validator';
import { GENDER_TYPES, USER_APP_PERMISSIONS } from 'models/user';
import { DIET_PREFERENCE } from 'models/user/user.schema';
import { CreateDeviceConnectionReqDTO } from 'src/user/device/device-dto';

export class CompleteUserProfileReqDTO {
  @ApiProperty({
    description: 'The age of the user',
    type: Number,
  })
  @IsInt()
  @Min(3, {
    message: 'The age you entered is outside the acceptable range (3-130).',
  })
  @Max(130, {
    message: 'The age you entered is outside the acceptable range (3-130).',
  })
  @IsNotEmpty()
  age: number;

  @ApiProperty({
    description: 'The gender of the user (e.g., male, female, other)',
    type: String,
  })
  @IsEnum(GENDER_TYPES, {
    message:
      'Invalid input for gender. Accepted values are Male, Female, and Other.',
  })
  @IsNotEmpty()
  gender: GENDER_TYPES;

  @ApiProperty({
    description: 'The height of the user in cm',
    type: Number,
  })
  @Min(50, {
    message:
      'The height you entered is outside the acceptable range (50-500 cm).',
  })
  @Max(500, {
    message:
      'The height you entered is outside the acceptable range (50-500 cm).',
  })
  @IsNotEmpty()
  height: number;

  @ApiProperty({
    description: 'The weight of the user in kilograms',
    type: Number,
  })
  @IsNumber()
  @Min(5, {
    message:
      'The Weight you entered is outside the acceptable range (5-400 kg).',
  })
  @Max(400, {
    message:
      'The Weight you entered is outside the acceptable range (5-400 kg)',
  })
  @IsNotEmpty()
  weight: number;

  @ApiProperty({
    description: 'Diet preference of the user (e.g., vegetarian, vegan, etc.)',
    type: String,
  })
  @IsEnum(DIET_PREFERENCE, { message: 'Please provide valid diet preference' })
  @IsNotEmpty()
  diet_preference: DIET_PREFERENCE;

  @ApiProperty({
    description: 'The physical goal of user',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  physical_goal: string;

  @ApiProperty({
    description: 'The activity goal of user',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  activity_goal: string;

  @ApiProperty({
    description: 'The mind goal of user',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  mind_goal: string;

  @ApiProperty({
    description: 'The sleep goal of user',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  sleep_goal: string;

  @ApiProperty({
    type: CreateDeviceConnectionReqDTO,
  })
  @IsOptional()
  deviceData: CreateDeviceConnectionReqDTO;

  @ApiProperty({
    example: 2,
    description: 'Device usage limit (must be between 2 and 3) or null',
    minimum: 2,
    maximum: 3,
    nullable: true,
  })
  @ValidateIf(
    (obj) =>
      obj.deviceUsageLimit !== null && obj.deviceUsageLimit !== undefined,
  )
  @IsNumber()
  @Min(2)
  @Max(3)
  @IsNotEmpty()
  deviceUsageLimit: number | null;

  @ApiProperty({
    description: 'The app permissions of user',
    type: [String],
  })
  @IsEnum(USER_APP_PERMISSIONS, {
    each: true,
    message: 'Invalid app permission provided.',
  }) // Ensure each item in the array is a valid enum value
  @IsArray({ message: 'App permissions must be an array.' })
  @IsNotEmpty()
  app_permissions: USER_APP_PERMISSIONS[];
}
