import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber } from 'class-validator';
import { NUTRITION_QUANTITY } from 'models/recipe';

export class RecipesNutritionByQuantityDTO {
  @ApiProperty({
    description: 'Quantity size',
    enum: NUTRITION_QUANTITY,
    example: NUTRITION_QUANTITY.MEDIUM,
  })
  @IsEnum(NUTRITION_QUANTITY)
  @IsNotEmpty()
  quantity: NUTRITION_QUANTITY;

  @ApiProperty({ description: 'Amount of protein in grams', example: 25 })
  @IsNumber()
  @IsNotEmpty()
  protein: number;

  @ApiProperty({ description: 'Amount of calories in kcal', example: 25 })
  @IsNumber()
  @IsNotEmpty()
  calories: number;

  @ApiProperty({ description: 'Amount of fats in grams', example: 10 })
  @IsNumber()
  @IsNotEmpty()
  fats: number;

  @ApiProperty({ description: 'Amount of fiber in grams', example: 5 })
  @IsNumber()
  @IsNotEmpty()
  fiber: number;

  @ApiProperty({ description: 'Amount of carbohydrates in grams', example: 50 })
  @IsNumber()
  @IsNotEmpty()
  carbs: number;

  static transform(
    object: RecipesNutritionByQuantityDTO,
  ): RecipesNutritionByQuantityDTO {
    const transformedObj = new RecipesNutritionByQuantityDTO();
    transformedObj.quantity = object.quantity as NUTRITION_QUANTITY;
    transformedObj.protein = object.protein;
    transformedObj.calories = object.calories;
    transformedObj.fats = object.fats;
    transformedObj.fiber = object.fiber;
    transformedObj.carbs = object.carbs;
    return transformedObj;
  }
}
