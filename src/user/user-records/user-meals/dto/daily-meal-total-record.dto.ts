import { MealDailyTotalRecord } from 'models/user-records/Meal-Records';

export class DailyMealTotalRecordDTO {
  id: string;
  month: number;
  year: number;
  date: Date;
  protein: number;
  calories: number;
  fats: number;
  fiber: number;
  carbs: number;

  static transform(
    object: MealDailyTotalRecord,
    showFullData: boolean = false,
  ): DailyMealTotalRecordDTO {
    const transformedObj = new DailyMealTotalRecordDTO();

    transformedObj.id = object._id.toString();

    if (showFullData) {
      transformedObj.month = object.month;
      transformedObj.year = object.year;
      transformedObj.date = object.date;
    }

    transformedObj.protein = object.protein;
    transformedObj.calories = object.calories;
    transformedObj.fats = object.fats;
    transformedObj.fiber = object.fiber;
    transformedObj.carbs = object.carbs;

    return transformedObj;
  }
}
