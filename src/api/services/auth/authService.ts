import axios from 'axios';

import { BASE_URL, getAPIConfig } from '../api-utils';

import { logInReqObj } from './types';

const ApiObj = {
  logIn: `${BASE_URL}/admin/login`,
  logOut: `${BASE_URL}/admin/logout`,
  getProfile: `${BASE_URL}/me`,
} as const;

export const LogInApi = async (data: logInReqObj) => {
  return await axios.post(ApiObj.logIn, data);
};

export const LogOutApi = async (accessToken: string) => {
  return await axios.get(ApiObj.logOut, getAPIConfig(accessToken));
};

export const GetProfileApi = async (accessToken: string) => {
  return await axios.get(ApiObj.getProfile, getAPIConfig(accessToken));
};
