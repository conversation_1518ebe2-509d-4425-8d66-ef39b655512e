import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AdminOtpToken } from 'models/auth';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { EmailService } from 'src/common/services/email.service';
import { EncryptionService } from 'src/common/services';
import { AdminOtpUtilsService } from './admin-otp.utils.service';
import { CustomConfigService } from 'src/common/services';
import { User } from 'models/user';
import { HttpStatus } from '@nestjs/common';
import { VerifyOtpResponseDto } from './dto';

function generate6DigitOtp(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

@Injectable()
export class AdminOtpService {
  constructor(
    @InjectModel(AdminOtpToken.name)
    private readonly otpModel: Model<AdminOtpToken>,
    private readonly emailService: EmailService,
    private readonly jwtService: JwtService,
    private readonly adminOtpUtilsService: AdminOtpUtilsService,
    private readonly encryptionService: EncryptionService,
  ) {}

  async sendOtp(user: User): Promise<void> {
    const otp = generate6DigitOtp();
    const expiry = Date.now() + 10 * 60 * 1000; // 10 minutes

    await this.otpModel.create({ userId: user._id, otp, expiry });

    const { subject, emailBody, fromEmail, AdminOtpEmailTemplate } =
      CustomConfigService.PROPERTIES.adminOtpEmail;

    const html = AdminOtpEmailTemplate.replace('$$email', user.name || 'Admin')
      .replace('$$otp', otp)
      .replace('$$timeLeftMessage', '10 Minutes');

    const textBody = emailBody.replace('$$otp', otp);

    await this.emailService.sendTextMail({
      fromEmail,
      toEmail: user.email,
      subject,
      textBody,
      html,
    });
  }

  async verifyOtp(user, otp: string): Promise<VerifyOtpResponseDto> {
    console.log('req, body', user._id, otp);
    const now = Date.now();
    const userId = user._id.toString();

    const otpDoc = await this.otpModel
      .findOne({ userId })
      .sort({ createdAt: -1 });

    if (!otpDoc) {
      throw new NotFoundException('OTP not found');
    }

    if (otpDoc.isExpired) {
      throw new BadRequestException('OTP has expired');
    }

    if (otpDoc.expiry.getTime() < now) {
      await this.otpModel.updateOne(
        { _id: otpDoc._id },
        { $set: { isExpired: true } },
      );
      throw new BadRequestException('OTP has expired');
    }

    const decryptedOtp = this.encryptionService.decrypt(otpDoc.otp);

    if (decryptedOtp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    await this.otpModel.updateOne(
      { _id: otpDoc._id },
      { $set: { isExpired: true } },
    );

    const { token, expiryInMs } =
      await this.adminOtpUtilsService.generateRouteAccessToken(user);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      accessToken: token,
      expiresAt: expiryInMs,
      message: 'OTP verified successfully',
    };
  }
}
