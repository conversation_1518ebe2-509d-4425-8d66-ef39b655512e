import { Injectable, NotFoundException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ContactUsQuery } from 'models/contact-us-query';
import { GetAllContactUsQueryQueryInterface } from './interfaces';
import {
  GetAllContactUsQueryResDTO,
  GetSingleContactUsQueryResDTO,
  UpdateContactUsQueryReqDTO,
  UpdateContactUsQueryResDTO,
} from './dto';
import { EncryptionService, UtilsService } from 'src/common/services';
import { UserRepoService } from 'src/repo/user-repo.service';
import { ContactUsQueryDTO } from 'src/user/contact_us/dto';

@Injectable()
export class AdminContactUsQueryService {
  constructor(
    @InjectModel(ContactUsQuery.name)
    private readonly contactUsQueryModel: Model<ContactUsQuery>,

    private readonly utilsService: UtilsService,
    private readonly userRepo: UserRepoService,
    private readonly encryptionService: EncryptionService,
  ) {}

  // Get all contact us queries
  async getAllQueries(
    filterQueriesData: GetAllContactUsQueryQueryInterface,
  ): Promise<GetAllContactUsQueryResDTO> {
    const { page, email, status } = filterQueriesData;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const filterObj: any = {};

    if (email) {
      const user = await this.userRepo.findUserByEmail(email);

      filterObj.userId = { $eq: user?._id || '' };
    }

    if (status) {
      filterObj.status = { $eq: status };
    }

    const queries = await this.contactUsQueryModel
      .find(filterObj)
      .populate('userId')
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.contactUsQueryModel
      .countDocuments(filterObj)
      .exec();

    const resp = queries.map((query) => {
      const { userId, ...rest } = query.toObject();
      const item = { ...rest, user: userId };

      (item.user as any).email = this.encryptionService.decrypt(
        (item.user as any).email,
      );

      return ContactUsQueryDTO.transform(item);
    });

    return {
      error: false,
      statusCode: HttpStatus.OK,
      nbHits: resp.length,
      total: total,
      queries: resp,
    };
  }

  // Get a single contact us query by its ID
  async getSingleQuery(
    queryId: string,
  ): Promise<GetSingleContactUsQueryResDTO> {
    const query = await this.contactUsQueryModel
      .findById(queryId)
      .populate('userId')
      .exec();

    if (!query) {
      throw new NotFoundException(`Query with ID ${queryId} not found.`);
    }

    const { userId, ...rest } = query.toObject();
    const item = { ...rest, user: userId };

    (item.user as any).email = this.encryptionService.decrypt(
      (item.user as any).email,
    );

    const resp = ContactUsQueryDTO.transform(item);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      query: resp,
    };
  }

  // Update the status of a contact us query
  async updateQuery(
    queryId: string,
    updateData: UpdateContactUsQueryReqDTO,
  ): Promise<UpdateContactUsQueryResDTO> {
    const { status } = updateData;

    const query = await this.contactUsQueryModel
      .findById(queryId)
      .populate('userId')
      .exec();

    if (!query) {
      throw new NotFoundException(`Query with ID ${queryId} not found.`);
    }

    // Update only the status field
    query.status = status;
    await query.save();

    const { userId, ...rest } = query.toObject();
    const item = { ...rest, user: userId };

    (item.user as any).email = this.encryptionService.decrypt(
      (item.user as any).email,
    );

    const resp = ContactUsQueryDTO.transform(item);

    return {
      error: false,
      statusCode: HttpStatus.OK,
      query: resp,
    };
  }
}
