import {
  Controller,
  Get,
  Query,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { AdminUsersService } from './admin-users.service';
import { getAllUsersQueryInterface } from './interface';
import { Authority } from 'src/utils/decorators';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { GetAllUsersResDTO } from './dto';
import { GetSingleUsersResDTO } from './dto';
import { DeleteUserResDTO } from './dto';

@ApiTags('Admin-Users')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('/admin/users')
export class AdminUsersController {
  constructor(private readonly adminUserService: AdminUsersService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the user profiles.',
    type: GetAllUsersResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    description: 'The page number for pagination (optional)',
    required: false,
    type: String,
    example: '1', // Example for Swagger documentation
  })
  @Authority()
  @Get()
  async getAllUsers(@Query() filterQueries: getAllUsersQueryInterface) {
    return this.adminUserService.getAllUsers(filterQueries);
  }

  // Get a single user by ID
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the user profile.',
    type: GetSingleUsersResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found.',
    type: ErrorResponse,
  })
  @Authority()
  @Get(':userId')
  async getSingleUser(@Param('userId') id: string) {
    return this.adminUserService.getSingleUser(id);
  }

  // Delete a user by ID
  @ApiResponse({
    status: 200,
    description: 'User successfully deleted.',
    type: DeleteUserResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Delete(':userId')
  async deleteUser(@Param('userId') id: string) {
    return this.adminUserService.deleteUser(id);
  }
}
