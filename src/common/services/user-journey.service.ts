import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { UserJourneyPerDay } from 'models/user-journey';
import { Model } from 'mongoose';

@Injectable()
export class UserJourneyService {
  constructor(
    @InjectModel(UserJourneyPerDay.name)
    private readonly userJourneyModel: Model<UserJourneyPerDay>,
  ) {}

  async ensureUserJourney(userId: string, age: number): Promise<void> {
    const today = new Date();
    const currentDate = new Date(today.setHours(0, 0, 0, 0));

    const existingJourney = await this.userJourneyModel.findOne({
      userId,
      age,
      createdAt: { $gte: currentDate },
    });

    if (!existingJourney) {
      const newJourney = new this.userJourneyModel({
        userId,
        age,
        createdAt: currentDate,
      });

      await newJourney.save();
    }

    return;
  }
}
