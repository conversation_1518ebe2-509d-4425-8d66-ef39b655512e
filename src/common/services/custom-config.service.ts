import { Injectable } from '@nestjs/common';
import config from '../properties';

@Injectable()
export class CustomConfigService {
  static PROPERTIES: any = (() => {
    if (process.env.NODE_ENV == 'development' || !process.env.NODE_ENV) {
      return config.development();
    }

    if (process.env.NODE_ENV == 'production') {
      return config.production();
    }

    // Default to development
    return config.development();
  })();

  constructor() {
    // The constructor is no longer responsible for setting the static PROPERTIES.
  }
}
