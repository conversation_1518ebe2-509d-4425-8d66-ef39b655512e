import { BadRequestException, Injectable } from '@nestjs/common';
import { REMINDER_TIME_PERIOD } from 'models/reminders';
import * as moment from 'moment-timezone';

@Injectable()
export class UtilsService {
  parsePageNumberAndGetlimitAndOffset(
    page: string,
    limit: number = 10,
  ): {
    offset: number;
    limit: number;
  } {
    const pageNumber = Math.max(parseInt(page, 10) || 1, 1);
    const offset = (pageNumber - 1) * limit;

    return {
      offset,
      limit,
    };
  }

  convertToGMT(
    userHour: number,
    userMinute: number,
    period: REMINDER_TIME_PERIOD,
    userTimeZone: string,
  ) {
    // Verify if the provided time zone is valid
    if (!moment.tz.zone(userTimeZone)) {
      throw new BadRequestException('Invalid Time Zone provided !!');
    }

    // Convert 12-hour format to 24-hour format
    let hour24 = userHour;
    if (period.toUpperCase() === 'PM' && userHour < 12) {
      hour24 += 12;
    } else if (period.toUpperCase() === 'AM' && userHour === 12) {
      hour24 = 0;
    }

    // Convert back to AM/PM format for validation
    const convertedPeriod = hour24 >= 12 ? 'PM' : 'AM';
    const convertedHour12 = hour24 % 12 === 0 ? 12 : hour24 % 12;

    // Check if the user-provided period matches the calculated period
    if (
      convertedPeriod !== period.toUpperCase() ||
      convertedHour12 !== userHour
    ) {
      throw new BadRequestException(
        'Mismatch between provided hour/period and actual conversion.',
      );
    }

    // Create a moment object with the user's provided time and timezone
    const userMoment = moment.tz(
      { hour: hour24, minute: userMinute },
      userTimeZone,
    );

    // Convert to GMT
    const gmtMoment = userMoment.clone().tz('Etc/GMT');

    // Convert GMT time to 12-hour format with AM/PM
    const gmtHour24 = gmtMoment.hour();
    const gmtMinute = gmtMoment.minute();
    const gmtPeriod = gmtHour24 >= 12 ? 'PM' : 'AM';
    const gmtHour12 = gmtHour24 % 12 === 0 ? 12 : gmtHour24 % 12;

    // Return GMT hour, minute, and period
    return {
      gmtHour: gmtHour12,
      gmtMinute: gmtMinute,
      gmtPeriod: gmtPeriod,
    };
  }
}
