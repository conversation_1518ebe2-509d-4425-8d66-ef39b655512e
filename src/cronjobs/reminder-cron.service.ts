import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ActiveMQService } from 'src/third-party/activeMQ/activeMQ.service';
import { ReminderSettings, ReminderTime } from 'models/reminders';
import { NotificationToken } from 'models/notificationToken';
import {
  NOTIFICATION_MESSAGE_STATUS,
  NotificationMessage,
} from 'models/notificationToken/notification-messages.schema';
import { getRandomReminderMessage } from '../utils/reminders/randomCustomMessageArray';
import { REMINDER_CATEGORY_ENUM } from 'src/user/reminders/reminders-utils.service';
import { dummyReminderMessages } from '../utils/reminders/randomCustomMessageArray';

// Add constant for dummy reminder category
const DUMMY_REMINDER_CATEGORY = 'FOLLOW_UP_MASSAGE';

@Injectable()
export class ReminderCronJob {
  constructor(
    @InjectModel(ReminderSettings.name)
    private readonly reminderModel: Model<ReminderSettings>,

    @InjectModel(NotificationToken.name)
    private readonly notificationTokenModel: Model<NotificationToken>,

    @InjectModel(NotificationMessage.name)
    private readonly notificationMessageModel: Model<NotificationMessage>,

    private readonly activeMqService: ActiveMQService,
  ) {}

  @Cron('*/15 * * * *')
  async sendReminderNotifications() {
    const nowUTC = new Date();
    const startTime = new Date(nowUTC.getTime() + 0 * 60000);
    const endTime = new Date(nowUTC.getTime() + 20 * 60000);

    const startComponents = this.extractTimeComponents(startTime);
    const endComponents = this.extractTimeComponents(endTime);

    const reminders = await this.getRelevantReminders(
      startComponents,
      endComponents,
    );

    for (const reminder of reminders) {
      await this.processReminder(reminder, nowUTC);
    }
  }

  private async getRelevantReminders(startComponents, endComponents) {
    const baseQuery = {
      isDeleted: false,
    };

    if (startComponents.period === endComponents.period) {
      if (startComponents.hour === endComponents.hour) {
        return this.reminderModel.find({
          ...baseQuery,
          'GMT_Time.period': startComponents.period,
          'GMT_Time.hour': startComponents.hour,
          'GMT_Time.minute': {
            $gt: startComponents.minute,
            $lte: endComponents.minute,
          },
        });
      } else {
        return this.reminderModel.find({
          ...baseQuery,
          'GMT_Time.period': startComponents.period,
          $or: [
            {
              'GMT_Time.hour': {
                $gt: startComponents.hour,
                $lt: endComponents.hour,
              },
            },
            {
              'GMT_Time.hour': startComponents.hour,
              'GMT_Time.minute': { $gt: startComponents.minute },
            },
            {
              'GMT_Time.hour': endComponents.hour,
              'GMT_Time.minute': { $lte: endComponents.minute },
            },
          ],
        });
      }
    } else {
      return this.reminderModel.find({
        ...baseQuery,
        $or: [
          {
            'GMT_Time.period': startComponents.period,
            $or: [
              {
                'GMT_Time.hour': { $gt: startComponents.hour },
              },
              {
                'GMT_Time.hour': startComponents.hour,
                'GMT_Time.minute': { $gt: startComponents.minute },
              },
            ],
          },
          {
            'GMT_Time.period': endComponents.period,
            $or: [
              {
                'GMT_Time.hour': { $lt: endComponents.hour },
              },
              {
                'GMT_Time.hour': endComponents.hour,
                'GMT_Time.minute': { $lte: endComponents.minute },
              },
            ],
          },
        ],
      });
    }
  }

  private async processReminder(reminder: ReminderSettings, nowUTC: Date) {
    const notificationTokens = await this.notificationTokenModel.find({
      userId: reminder.userId,
      isNotificationActive: true,
      isActive: true,
    });

    if (!notificationTokens.length) {
      return;
    }

    for (const notificationToken of notificationTokens) {
      await this.sendNotification(reminder, notificationToken, nowUTC);
    }
  }

  private async sendNotification(
    reminder: ReminderSettings,
    token: NotificationToken,
    nowUTC: Date,
  ) {
    const actualReminderTime = this.convertReminderTimeToDate(
      reminder.GMT_Time,
    );
    const delay = Math.max(actualReminderTime.getTime() - nowUTC.getTime(), 0);

    const baseFields = {
      userId: reminder.userId,
      notificationTokenId: token.id,
      reminderSettingsId: reminder.id,
      status: NOTIFICATION_MESSAGE_STATUS.PENDING,
      isDeleted: false,
    };

    const actualFilter = {
      ...baseFields,
      category: reminder.categoryString,
      label: reminder.label,
      isDummyReminder: false,
    };

    // === ACTUAL REMINDER ===
    try {
      const msg = await this.notificationMessageModel
        .findOne({
          ...actualFilter,
          status: {
            $in: [
              NOTIFICATION_MESSAGE_STATUS.PENDING,
              NOTIFICATION_MESSAGE_STATUS.SUCCESS,
            ],
          },
        })
        .sort({ queuedAt: -1 });

      if (
        msg &&
        msg.queuedAt &&
        msg.queuedAt >= new Date(Date.now() - 20 * 60 * 1000)
      ) {
        return;
      }

      const actualMsg = await this.notificationMessageModel.findOneAndUpdate(
        actualFilter,
        {
          $setOnInsert: {
            messageTitle: reminder.label,
            messageBody: getRandomReminderMessage(reminder.categoryString),
            scheduledAt: actualReminderTime,
            sound: reminder.sound,
            frontend_screen_url: reminder.frontend_screen_url,
            GMT_Time: reminder.GMT_Time,
          },
          $set: {
            isQueued: true,
            queuedAt: new Date(),
          },
        },
        { upsert: true, new: true },
      );

      this.activeMqService.sendMessageWithDelay(
        'reminder-delay-queue',
        actualMsg,
        delay,
      );
    } catch (err) {
      if (err.code === 11000) {
        console.warn('[ReminderCron] Duplicate actual reminder skipped.');
      } else {
        throw err;
      }
    }

    // === DUMMY REMINDER ===
    if (reminder.categoryString === REMINDER_CATEGORY_ENUM.FOOD) {
      const dummyLabel = `${reminder.label} - follow up`;
      const dummyScheduledTime = new Date(
        actualReminderTime.getTime() + 10 * 60 * 1000,
      );

      const effectiveDummyDelay = Math.max(
        dummyScheduledTime.getTime() - nowUTC.getTime(),
        0,
      );

      const dummyFilter = {
        ...baseFields,
        category: DUMMY_REMINDER_CATEGORY,
        label: dummyLabel,
        isDummyReminder: true,
      };

      try {
        const dummyMsg = await this.notificationMessageModel.findOneAndUpdate(
          dummyFilter,
          {
            $setOnInsert: {
              messageTitle: 'It’s time to use your massage device!',
              messageBody:
                dummyReminderMessages[
                  Math.floor(Math.random() * dummyReminderMessages.length)
                ],
              scheduledAt: dummyScheduledTime,
              sound: reminder.sound,
              frontend_screen_url: reminder.frontend_screen_url,
              GMT_Time: reminder.GMT_Time,
            },
            $set: {
              isQueued: true,
              queuedAt: new Date(),
            },
          },
          { upsert: true, new: true },
        );

        this.activeMqService.sendMessageWithDelay(
          'reminder-delay-queue',
          dummyMsg,
          effectiveDummyDelay,
        );
      } catch (err) {
        if (err.code === 11000) {
          console.warn('[ReminderCron] Duplicate dummy reminder skipped.');
        } else {
          throw err;
        }
      }
    }
  }

  private extractTimeComponents(date: Date) {
    let hours = date.getUTCHours();
    const minutes = date.getUTCMinutes();
    const period = hours >= 12 ? 'PM' : 'AM';

    if (hours > 12) hours -= 12;
    if (hours === 0) hours = 12;

    return { hour: hours, minute: minutes, period };
  }

  private convertReminderTimeToDate(reminder: ReminderTime): Date {
    let hours = reminder.hour;

    if (reminder.period === 'PM' && hours !== 12) {
      hours += 12;
    } else if (reminder.period === 'AM' && hours === 12) {
      hours = 0;
    }

    const date = new Date();
    date.setUTCHours(hours, reminder.minute, 0, 0);
    return date;
  }
}
