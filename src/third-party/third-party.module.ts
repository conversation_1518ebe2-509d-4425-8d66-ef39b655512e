import { Module } from '@nestjs/common';
import { AwsS3Service, AwsSesService } from './aws';
import { ExpoNotificationService } from './expoNotification/expoNotification.service';
import { CustomLogger } from 'src/common/services';
import { OpenAIService } from './openAI/openai.service';

@Module({
  providers: [
    AwsSesService,
    AwsS3Service,
    ExpoNotificationService,
    CustomLogger,
    OpenAIService,
  ],
  exports: [
    AwsSesService,
    AwsS3Service,
    ExpoNotificationService,
    OpenAIService,
  ],
})
export class ThirdPartyModule {}
