import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NotificationMessage } from 'models/notificationToken/notification-messages.schema';
import fetch from 'node-fetch';
import { CustomLogger } from 'src/common/services';

@Injectable()
export class ExpoNotificationService {
  constructor(
    private readonly logger: CustomLogger,
    private readonly configService: ConfigService,
  ) {}

  private EXPO_PUSH_URL = this.configService.get<string>('EXPO_PUSH_SEND_URL');

  async sendPushNotification(
    token: string,
    notificationMessage: NotificationMessage,
  ) {
    if (!token.startsWith('ExponentPushToken')) {
      throw new InternalServerErrorException('Invalid Expo Push Token');
    }

    const message: any = {
      to: token,
      title: notificationMessage.messageTitle,
      body: notificationMessage.messageBody,
      channelId: notificationMessage.sound.android,
      sound: notificationMessage.sound.ios,
      data: { url: notificationMessage.frontend_screen_url },
    };

    try {
      const response = await fetch(this.EXPO_PUSH_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(message),
      });

      const result = await response.json();

      if (result.data && result.data.id) {
        const receiptResponse = await fetch(
          this.configService.get<string>('EXPO_PUSH_RECEIPT_URL'),
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ids: [result.data.id] }),
          },
        );

        const receiptResult = await receiptResponse.json();

        this.logger.log(`'Expo Push Receipt:', ${receiptResult}`);
      }
    } catch (error) {
      console.error('Error sending Expo notification:', error);
    }
  }
}
