import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum CONTACTUS_ATTACHMENTS_TYPES {
  VIDEO = 'video',
  IMAGE = 'image',
}

@Schema({ timestamps: true })
export class ContactUs_Query_Attachments extends Document {
  @Prop({ type: String, required: true })
  url: string;

  @Prop({ type: String, enum: CONTACTUS_ATTACHMENTS_TYPES, required: true })
  type: CONTACTUS_ATTACHMENTS_TYPES;

  createdAt: Date;

  updatedAt: Date;
}

export const ContactUs_Query_Attachments_Schema = SchemaFactory.createForClass(
  ContactUs_Query_Attachments,
);
