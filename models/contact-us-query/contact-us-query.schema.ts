import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'models/user';
import {
  ContactUs_Query_Attachments,
  ContactUs_Query_Attachments_Schema,
} from './contact-us-query-attachments.schema';

export enum QUERY_CATEGORY {
  APPLICATION = 'application',
  ACCOUNT = 'account',
  DEVICE = 'device',
  FEEDBACK = 'feedback',
}

export enum QUERY_STATUS {
  PENDING = 'pending',
  RESOLVED = 'resolved',
}

@Schema({ timestamps: true })
export class ContactUsQuery extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: String, enum: QUERY_CATEGORY, required: true })
  category: QUERY_CATEGORY;

  @Prop({ type: String, required: true })
  description: string;

  @Prop({ type: [ContactUs_Query_Attachments_Schema], required: true })
  attachments: ContactUs_Query_Attachments[];

  @Prop({ type: String, enum: QUERY_STATUS, default: QUERY_STATUS.PENDING })
  status: QUERY_STATUS;

  createdAt: Date;

  updatedAt: Date;
}

export const ContactUsQuerySchema =
  SchemaFactory.createForClass(ContactUsQuery);
