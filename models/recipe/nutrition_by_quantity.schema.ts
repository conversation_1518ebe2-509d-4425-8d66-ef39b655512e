import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum NUTRITION_QUANTITY {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

@Schema({ timestamps: true })
export class RecipesNutritionByQuantity extends Document {
  @Prop({ type: String, enum: NUTRITION_QUANTITY, required: true })
  quantity: NUTRITION_QUANTITY;

  @Prop({ type: Number, required: true, min: 0 })
  protein: number;

  @Prop({ type: Number, required: true, min: 0 })
  calories: number;

  @Prop({ type: Number, required: true, min: 0 })
  fats: number;

  @Prop({ type: Number, required: true, min: 0 })
  fiber: number;

  @Prop({ type: Number, required: true, min: 0 })
  carbs: number;

  createdAt: Date;

  updatedAt: Date;
}

export const RecipesNutritionByQuantitySchema = SchemaFactory.createForClass(
  RecipesNutritionByQuantity,
);
