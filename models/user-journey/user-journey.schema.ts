import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class UserJourneyPerDay extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  userId: Types.ObjectId;

  @Prop({ required: true })
  age: number;

  createdAt: Date;
  updatedAt: Date;
}

export const UserJourneyPerDaySchema =
  SchemaFactory.createForClass(UserJourneyPerDay);
