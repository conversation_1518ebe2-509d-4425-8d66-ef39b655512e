import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

export enum MOOD_TYPES {
  HAPPY = 'happy',
  MODERATELY_HAPPY = 'moderately happy',
  IRRITATED = 'irritated',
  ANXIOUS = 'anxious',
  SAD = 'sad',
}

export enum HUNGER_LEVELS {
  MILD = 'mild',
  BARELY = 'barely',
  MODERATE = 'moderate',
  HIGH = 'high',
}

@Schema({ timestamps: true })
export class UserMoodRecords extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: String, enum: MOOD_TYPES, required: true })
  moodType: string;

  @Prop({ type: String, enum: HUNGER_LEVELS, required: true })
  hungerLevel: string;

  createdAt: Date;

  updatedAt: Date;
}

export const UserMoodRecordsSchema =
  SchemaFactory.createForClass(UserMoodRecords);
