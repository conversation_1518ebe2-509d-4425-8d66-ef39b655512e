import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user/user.schema';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class DailyMoodAverage extends Document {
  @Prop({ type: Types.ObjectId, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Number, required: true })
  month: number;

  @Prop({ type: Number, required: true })
  date: number;

  @Prop({ type: Number, required: true })
  year: number;

  @Prop({ type: Number, required: true })
  moodTypeScore: number;

  @Prop({ type: Number, required: true })
  hungerLevelScore: number;

  @Prop()
  createdAt: Date;

  @Prop()
  updatedAt: Date;
}

export const DailyMoodSchema = SchemaFactory.createForClass(DailyMoodAverage);
