import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export enum REMINDER_TIME_PERIOD {
  AM = 'AM',
  PM = 'PM',
}

@Schema({ timestamps: true })
export class ReminderTime {
  @Prop({ type: Number, min: 1, max: 12, required: true })
  hour: number;

  @Prop({ type: Number, min: 0, max: 59, required: true })
  minute: number;

  @Prop({ type: String, enum: REMINDER_TIME_PERIOD, required: true })
  period: string;

  createdAt: Date;

  updatedAt: Date;
}

export const ReminderTimeSchema = SchemaFactory.createForClass(ReminderTime);
