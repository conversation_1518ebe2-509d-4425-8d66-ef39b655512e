import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ReminderTime, ReminderTimeSchema } from './reminder-time.schema';
import {
  Reminders_Sound,
  Reminders_SoundSchema,
} from './reminder-sound.schema';

@Schema({ timestamps: true })
export class ReminderSettings extends Document {
  @Prop({ type: String, required: true })
  categoryId: string;

  @Prop({ type: String, required: true })
  categoryString: string;

  @Prop({ type: String, required: true })
  label: string;

  @Prop({ type: Reminders_SoundSchema, required: true })
  sound: Reminders_Sound;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  @Prop({ type: String, required: true })
  userId: string;

  @Prop({ type: ReminderTimeSchema, required: true })
  userTime: ReminderTime;

  @Prop({ type: String, required: true })
  timeZone: string;

  @Prop({ type: ReminderTimeSchema, required: true })
  GMT_Time: ReminderTime;

  @Prop({ type: String, required: true })
  frontend_screen_url: string;

  createdAt: Date;

  updatedAt: Date;
}

export const RemindersSettingsSchema =
  SchemaFactory.createForClass(ReminderSettings);
