#main server .env.dev
# App config
SERVER_PORT = 5000
NODE_ENV = 'development'
FRONTEND_URL = https://appetec.boilerplate.solutions

# Encryption
ENCRYPTION_KEY = 'c1b96d4$@$@#@#$#$%#^$&T^%&%^%^&%&&$%$$#$#@#'
ENCRYPTION_IV = 'a9ebc6af9d8f9a2f367bed5b7b3896b7'
ENCRYPTION_SALT = '10'

# Crypto Key
CRYPTO_KEY=9e8f8b1acdf21a1b6d03f83db0fe2b15a3ddaa0102030405060708090a0b0c0d

#Allowed IP
CHATBOT_ALLOWED_IPS = *************, ***********

# MONGO_USERNAME = mayankjoshi
# MONGO_PASSWORD = 61L85DSQ8hCy61Sr
# MONGO_HOST = appetec.2ef7g.mongodb.net
# MONGO_PORT = '27017'
# MONGO_DATABASE = 'appetec_db?retryWrites=true&w=majority&appName=Apptec'

# Production Mongo DB config
MONGO_USERNAME = ssedghi
MONGO_PASSWORD = c7uKVC3eyrmuIopY
MONGO_HOST = appetec.jbv3d.mongodb.net
MONGO_PORT = '27017'
MONGO_DATABASE = 'appetec_db?retryWrites=true&w=majority&appName=Appetec'

# Redis config
REDIS_TYPE = 'single'
REDIS_URL = 'redis://localhost:6379' # redis-server --port 6380

# verification token
VERIFICATION_TOKEN_EXPIRY = 86400000 # 24 hrs

# JWT
JWT_ACCESS_TOKEN_SECRET = 'ascjnaskcnasknckjnaskjnasjknckabcuiwqebeiqvqnpqpmwcq'
JWT_ACCESS_TOKEN_LIFETIME = '1h'
ACCESS_TOKEN_EXPIRY = 3600000  # 1 hr
JWT_REFRESH_TOKEN_SECRET = 'saknclansclanscnaslcknaiosjcanlsc'
JWT_REFRESH_TOKEN_LIFETIME = '7d'
REFRESH_TOKEN_EXPIRY = 604800000 # 7 days

# AWS Ses config
AWS_SES_ACCESS_KEY_ID = ********************
AWS_SES_SECRET_ACCESS_KEY = FxdygWGLoQ5kjow+87qO2oyCpg7AqHEkcZHqKuQU
AWS_SES_REGION = us-east-1
AWS_SES_EMAIL = <EMAIL>

# Aws S3 Config
AWS_S3_ACCESS_KEY_ID = ********************
AWS_S3_SECRET_ACCESS_KEY = FxdygWGLoQ5kjow+87qO2oyCpg7AqHEkcZHqKuQU
AWS_S3_REGION = us-east-1
AWS_S3_BUCKET_NAME = microcosmworkspoc

# ADMIN CREDENTIALS
APP_ADMIN_MAIL = '<EMAIL>'
APP_ADMIN_NAME = 'Admin'
APP_ADMIN_PASSWORD = 'Appetec@123'

# ActiveMQ
ACTIVE_MQ_HOSTNAME = 'localhost'
ACTIVE_MQ_PORT = 61613
ACTIVE_MQ_USER = 'admin'
ACTIVE_MQ_PASS = 'admin'

# EXPO
EXPO_PUSH_SEND_URL = 'https://exp.host/--/api/v2/push/send'
EXPO_PUSH_RECEIPT_URL = 'https://exp.host/--/api/v2/push/getReceipts'

# Consumer
CONSUMER_REMINDER_TTL = 5 # in minutes

# Chatbot Microservice
CHATBOT_MICROSERVICE_URL = https://appetec.boilerplate.solutions/chatbot-api
CHATBOT_MICROSERVICE_API_KEY = MICROSERVICE_API_KEY_558844

# Health Microservice
HEALTH_MICROSERVICE_URL = https://appetec.boilerplate.solutions/health-api
HEALTH_MICROSERVICE_API_KEY = MICROSERVICE_API_KEY_998877

# Open AI Key
OPENAI_API_KEY = ********************************************************************************************************************************************************************

# promt
MOOD_VIDEO_RECOMMANDATION_PROMPT = "Analyze the following user mood and hunger data from the past 24 hours: {moodRecords} Based on this data, determine the user's: - Mood (one of the following): sad, anxious, irritated, moderately happy, happy. - Hunger level (one of the following): mild, barely, moderate, high. Format the response strictly as: Mood: <one of the predefined mood values>; Hunger: <one of the predefined hunger values>. Do not include any additional text."
SLEEP_FEEDBACK_PROMPT = "You are a sleep coach analyzing a users sleep pattern over the last 7 days.\n\nUser Sleep Goal:\n- {sleepGoal}\n\nSleep Records (last 7 days):\n{sleepRecords}\n\nWrite exactly two short lines:\n1. An actionable sleep suggestion in 6-10 words.\n2. A motivational message in 6-10 words.\n\nNo bullet points, numbering, or extra explanation. Return only the two lines separated by a newline."
WEIGHT_FEEDBACK_PROMPT = "You are a fitness coach analyzing a user's weight data from the past week.\n\nUser Goal:\n- {goal}\n\nWeight Records (last 7 days):\n{weightRecords}\n\nBased on the user's goal and weekly data, write a concise 2-line highlight summarizing their progress and suggesting what they should do next. Be clear, motivational, and insightful."
MOOD_FEEDBACK_PROMPT = "Given the mood and hunger summary from the last 7 days: {moodRecords}, analyze the emotional and nutritional patterns and generate two short feedback messages. If mood is low or hunger irregular, provide a relevant suggestion. Each message should be 5-8 words long. Avoid numbering in the response. Example format: - Eat regularly to stabilize your mood - You're doing great, keep it up!"
MEAL_FEEDBACK_PROMPT = "You are a nutrition coach analyzing a users calorie intake over the past week. User Goal: {goal} Calorie Records (last 7 days): {calorieRecords} Based on the user's goal and weekly data, write exactly two short and actionable coaching tips (one per line). Each line should be concise, motivational, and easy to follow. Avoid summaries or long explanations. Respond with only the two tips, no introductions or extra text."
DEVICE_FEEDBACK_PROMPT="You are a fitness coach reviewing a user's usage of a fitness device over the past 7 days. The user has set a daily usage goal of {limit}. Device Usage Records (last 7 days): {usageRecords} Based on the user's goal and their weekly usage, provide exactly two short, motivational coaching tips to help them stay consistent and achieve their goals. Each tip should be one line only. Avoid summaries or explanations. Respond with only the two tips."